'use client';

import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { P2PDebugInterface } from '@/components/debug/P2PDebugInterface';

function P2PDebugPage() {
  const router = useRouter();

  return (
    <div className="container mx-auto py-6 px-4 max-w-6xl">
      {/* Navigation */}
      <button
        onClick={() => router.back()}
        className="mb-6 flex items-center text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
        type="button"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </button>

      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">🚀 Rock Solid Sync Debug Console</h1>
        <p className="text-muted-foreground">
          Real-time monitoring of simple HTTP discovery and direct PouchDB-to-CouchDB sync
        </p>
      </div>

      {/* Debug Interface */}
      <P2PDebugInterface />
    </div>
  );
}

export default P2PDebugPage;
