// DEPRECATED: This file will be renamed to .bak
// All existing sync systems are being deprecated and replaced with a simpler implementation
// DO NOT USE THIS FILE - IT WILL BE REMOVED

// DEPRECATED: This hook is no longer used.
// The mobile app now uses useAutonomousSync with SimpleIPDiscovery through AutonomousSyncProvider.
// This file is kept for reference but should not be imported or used.

import { useState, useEffect, useCallback } from 'react';
import { PeerInfo, SyncStatus } from '../../types/p2p-sync';
import { isMobileApp } from '../utils/environment';

// DEPRECATED: Global instance removed

/**
 * DEPRECATED: This hook is no longer functional.
 * Use useAutonomousSync with AutonomousSyncProvider instead.
 *
 * @deprecated Use useAutonomousSync from '@/lib/hooks/use-autonomous-sync'
 */
export function useMobileP2PSync(pouchDb: any, port: number = 8000) {
  console.error('[useMobileP2PSync] DEPRECATED: Use useAutonomousSync with AutonomousSyncProvider instead');
  // Return deprecated stub values
  return {
    isInitialized: false,
    desktopHubs: [],
    syncStatuses: [],
    error: new Error('useMobileP2PSync is deprecated. Use useAutonomousSync instead.'),
    logs: ['DEPRECATED: Use useAutonomousSync with AutonomousSyncProvider instead'],
    mdnsStatus: 'error' as const,
    initSteps: [],
    startSync: async () => { throw new Error('Deprecated'); },
    stopSync: async () => { throw new Error('Deprecated'); },
    stopAllSyncsWithHub: async () => { throw new Error('Deprecated'); },
    peers: [],
    stopAllSyncsWithPeer: async () => { throw new Error('Deprecated'); },
    multiMasterStatus: { enabled: false, databases: [], direction: 'both' as const },
    enableMultiMasterSync: () => { throw new Error('Deprecated'); },
    disableMultiMasterSync: () => { throw new Error('Deprecated'); },
    startMultiMasterSync: async () => { throw new Error('Deprecated'); },
    stopMultiMasterSync: async () => { throw new Error('Deprecated'); },
    configureMultiMaster: () => { throw new Error('Deprecated'); },
  };
}

