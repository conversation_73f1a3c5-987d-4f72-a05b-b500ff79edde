// DEPRECATED: This file will be renamed to .bak
// All existing sync systems are being deprecated and replaced with a simpler implementation
// DO NOT USE THIS FILE - IT WILL BE REMOVED

'use client';

import { useEffect, useRef } from 'react';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';
import { isMobileApp } from '@/lib/utils/environment';

export function AutonomousSyncProvider({ children }: { children: React.ReactNode }) {
  const { db: mainDbInstance, isReady } = useUnifiedDB();
  const hasStarted = useRef(false);
  
  // Initialize simple sync with error handling
  const { isActive, connections, stats, initStatus, errors, startSync } = useAutonomousSync(mainDbInstance, {
    enabled: true
  });

  // Auto-start sync when app loads (all platforms for debugging)
  useEffect(() => {
    if (!isReady || !mainDbInstance || hasStarted.current) return;
    
    const isMobile = isMobileApp();
    
    // Auto-start when initialization is ready (all platforms)
    if (!isActive && initStatus === 'ready') {
      console.log(`[AutonomousSyncProvider] Auto-starting sync for ${isMobile ? 'mobile' : 'desktop'} device`);
      hasStarted.current = true;
      
      // Start with a small delay to ensure everything is loaded
      setTimeout(() => {
        startSync();
      }, 2000);
    } else if (initStatus === 'failed') {
      console.error('[AutonomousSyncProvider] Cannot start sync - initialization failed:', errors);
    }
  }, [isReady, mainDbInstance, isActive, initStatus, startSync, errors]);

  // Log sync status changes
  useEffect(() => {
    if (isActive) {
      console.log(`[AutonomousSyncProvider] Sync active: ${stats.activeConnections} connections`);
    }
  }, [isActive, stats.activeConnections]);

  return <>{children}</>;
}